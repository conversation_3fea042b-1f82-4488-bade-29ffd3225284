import { Client } from '@elastic/elasticsearch';
import { logger } from '../utils/logger';
import { ProductType, CropGrowthStage, CropHealthStatus } from '../models/product.model';

const ELASTICSEARCH_NODE = process.env.ELASTICSEARCH_NODE || 'http://localhost:9200';
const PRODUCT_INDEX = process.env.ELASTICSEARCH_INDEX || 'befarma';

/**
 * Product document interface for Elasticsearch
 */
export interface ProductDocument {
  id: string;
  type: ProductType;
  sellerId: string;
  name: string;
  description: string;
  category: string;
  subCategory?: string;
  price: {
    amount: number;
    currency: string;
    unit?: string;
  };
  availability: {
    status: 'AVAILABLE' | 'OUT_OF_STOCK' | 'LIMITED';
    quantity?: number;
  };
  attributes: {
    // Common attributes
    images?: string[];
    tags?: string[];
    rating?: number;
    
    // Plot-specific attributes
    size?: number;
    location?: {
      country: string;
      state: string;
      city: string;
      pincode: string;
      addressLine1: string;
      addressLine2?: string;
      latitude?: number;
      longitude?: number;
    };
    soilType?: string;
    waterAvailability?: string;
    
    // Crop-specific attributes
    cropId?: string;
    cropCategory?: string;
    variety?: string;
    farmingMethod?: string;
    growthStage?: CropGrowthStage;
    irrigationMethod?: string;
    harvestSeason?: string;
    ownershipUnit?: string;
    pestDiseaseStatus?: string;
    storageMethod?: string;
    nutrientManagement?: string;
    waterSource?: string;
    pesticideUsage?: string;
    seedType?: string;
    cropHealthStatus?: CropHealthStatus;
    harvestingMethod?: string;
    packagingType?: string;
    transportationOptions?: string[];
    estimatedDeliveryTime?: string;
    deliveryCharges?: number;
    fulfillmentStatus?: string;
    riskFactors?: string[];

    plantingDate?: string; // ISO date string
    expectedHarvestDate?: string; // ISO date string
    actualHarvestDate?: string; // ISO date string

    health?: {
      status: CropHealthStatus;
      issues: string[];
      lastCheck: string; // ISO date string
    };

    yield?: {
      expected: number;
      actual: number;
      unit: string;
    };

    resources?: {
      water: number;
      fertilizer: number;
      pesticides: number;
    };

    soilConditions?: {
      type: string;
      ph: number;
      nutrients: string[];
      organicMatter?: number;
      drainage?: string;
      salinity?: string;
    };

    nutritionalInfo?: {
      calories?: number;
      protein?: number;
      carbs?: number;
      fiber?: number;
      vitamins?: string[];
      minerals?: string[];
      antioxidants?: string[];
    };

    cultivation?: {
      irrigationNeeds: string;
      fertilizerRequirements: string;
      pestControl: string;
      climateConditions: string;
      sowingMethod?: string;
      spacingRequirements?: string;
      companionCrops?: string[];
    };

    postHarvest?: {
      storageRequirements: string;
      shelfLife: string;
      processingMethods: string[];
      valueAddedProducts?: string[];
      marketDemand?: string;
      exportPotential?: boolean;
    };

    sustainability?: {
      waterUsage: string;
      carbonFootprint: string;
      pesticide: string;
      biodiversityImpact?: string;
      soilHealth?: string;
      renewableEnergy?: boolean;
    };

    maintenance?: {
      schedule: {
        irrigation: string[]; // Array of ISO date strings
        fertilization: string[]; // Array of ISO date strings
        pestControl: string[]; // Array of ISO date strings
        inspection: string[]; // Array of ISO date strings
      };
      history: {
        activities: {
          type: string;
          date: string; // ISO date string
          description: string;
          performedBy: string;
        }[];
      };
    };

    weather?: {
      forecasts: {
        date: string; // ISO date string
        temperature: {
          min: number;
          max: number;
          unit: string;
        };
        precipitation: {
          amount: number;
          unit: string;
          type: string;
        };
        humidity: number;
        windSpeed: {
          value: number;
          unit: string;
        };
      }[];
      alerts: {
        type: string;
        severity: string;
        description: string;
        startDate: string; // ISO date string
        endDate: string; // ISO date string
      }[];
    };

    market?: {
      demandLevel?: 'HIGH' | 'MEDIUM' | 'LOW';
      priceVolatility?: 'HIGH' | 'MEDIUM' | 'LOW';
      seasonalDemand?: string[];
      targetMarkets?: string[];
      competitionLevel?: 'HIGH' | 'MEDIUM' | 'LOW';
    };

    // Farmer-related attributes
    farmerExperienceLevel?: string;
    farmerCertification?: string;
    farmerReputationScore?: number;

    // Transaction-related attributes
    ownershipStatus?: string;
    paymentMethods?: string[];
    pricePerOwnershipUnit?: number;
    paymentTerms?: string;
    cancellationPolicy?: string;
    agreementDocumentUrl?: string;
    taxInformation?: string;

    // Platform usage
    listingCreationDate?: string; // ISO date string
    listingExpiryDate?: string; // ISO date string
    viewCount?: number;
    wishlistCount?: number;

    farmId?: string;
    plotId?: string;

    // Farm service-specific attributes
    serviceArea?: string;
    serviceDuration?: string;
    equipmentUsed?: string[];

    // Equipment/Supply specific attributes
    brand?: string;
    model?: string;
    condition?: string;
    expiryDate?: string; // ISO date string
  };
  certifications?: string[];
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  lastSyncedAt?: string; // ISO date string
}

/**
 * Product mapping for Elasticsearch
 */
export const productMapping = {
  mappings: {
    properties: {
      id: { type: 'keyword' },
      type: { 
        type: 'text',
        fields: {
          keyword: { type: 'keyword' }
        }
      },
      sellerId: { type: 'keyword' },
      name: {
        type: 'text',
        fields: {
          keyword: { type: 'keyword' }
        }
      },
      description: { type: 'text' },
      category: { type: 'keyword' },
      subCategory: { type: 'keyword' },
      price: {
        properties: {
          amount: { type: 'double' },
          currency: { type: 'keyword' },
          unit: { type: 'keyword' }
        }
      },
      availability: {
        properties: {
          status: { type: 'keyword' },
          quantity: { type: 'double' }
        }
      },
      attributes: {
        properties: {
          images: { type: 'keyword' },
          tags: { type: 'keyword' },
          rating: { type: 'float' },
          
          size: { type: 'double' },
          location: {
            properties: {
              country: { type: 'keyword' },
              state: { type: 'keyword' },
              city: { type: 'keyword' },
              pincode: { type: 'keyword' },
              addressLine1: { type: 'text' },
              addressLine2: { type: 'text' },
              latitude: { type: 'float' },
              longitude: { type: 'float' }
            }
          },
          soilType: { type: 'keyword' },
          waterAvailability: { type: 'keyword' },
          
          cropId: { type: 'keyword' },
          cropCategory: { type: 'keyword' },
          variety: { type: 'keyword' },
          farmingMethod: { type: 'keyword' },
          growthStage: { type: 'keyword' },
          irrigationMethod: { type: 'keyword' },
          harvestSeason: { type: 'keyword' },
          ownershipUnit: { type: 'keyword' },
          pestDiseaseStatus: { type: 'keyword' },
          storageMethod: { type: 'keyword' },
          nutrientManagement: { type: 'keyword' },
          waterSource: { type: 'keyword' },
          pesticideUsage: { type: 'keyword' },
          seedType: { type: 'keyword' },
          cropHealthStatus: { type: 'keyword' },
          harvestingMethod: { type: 'keyword' },
          packagingType: { type: 'keyword' },
          transportationOptions: { type: 'keyword' },
          estimatedDeliveryTime: { type: 'keyword' },
          deliveryCharges: { type: 'double' },
          fulfillmentStatus: { type: 'keyword' },
          riskFactors: { type: 'keyword' },

          plantingDate: { type: 'date' },
          expectedHarvestDate: { type: 'date' },
          actualHarvestDate: { type: 'date' },

          health: {
            properties: {
              status: { type: 'keyword' },
              issues: { type: 'keyword' },
              lastCheck: { type: 'date' }
            }
          },

          yield: {
            properties: {
              expected: { type: 'double' },
              actual: { type: 'double' },
              unit: { type: 'keyword' }
            }
          },

          resources: {
            properties: {
              water: { type: 'double' },
              fertilizer: { type: 'double' },
              pesticides: { type: 'double' }
            }
          },

          soilConditions: {
            properties: {
              type: { type: 'keyword' },
              ph: { type: 'float' },
              nutrients: { type: 'keyword' },
              organicMatter: { type: 'float' },
              drainage: { type: 'keyword' },
              salinity: { type: 'keyword' }
            }
          },

          nutritionalInfo: {
            properties: {
              calories: { type: 'integer' },
              protein: { type: 'float' },
              carbs: { type: 'float' },
              fiber: { type: 'float' },
              vitamins: { type: 'keyword' },
              minerals: { type: 'keyword' },
              antioxidants: { type: 'keyword' }
            }
          },

          cultivation: {
            properties: {
              irrigationNeeds: { type: 'keyword' },
              fertilizerRequirements: { type: 'keyword' },
              pestControl: { type: 'keyword' },
              climateConditions: { type: 'keyword' },
              sowingMethod: { type: 'keyword' },
              spacingRequirements: { type: 'keyword' },
              companionCrops: { type: 'keyword' }
            }
          },

          postHarvest: {
            properties: {
              storageRequirements: { type: 'keyword' },
              shelfLife: { type: 'keyword' },
              processingMethods: { type: 'keyword' },
              valueAddedProducts: { type: 'keyword' },
              marketDemand: { type: 'keyword' },
              exportPotential: { type: 'boolean' }
            }
          },

          sustainability: {
            properties: {
              waterUsage: { type: 'keyword' },
              carbonFootprint: { type: 'keyword' },
              pesticide: { type: 'keyword' },
              biodiversityImpact: { type: 'keyword' },
              soilHealth: { type: 'keyword' },
              renewableEnergy: { type: 'boolean' }
            }
          },

          maintenance: {
            properties: {
              schedule: {
                properties: {
                  irrigation: { type: 'date' },
                  fertilization: { type: 'date' },
                  pestControl: { type: 'date' },
                  inspection: { type: 'date' }
                }
              },
              history: {
                properties: {
                  activities: {
                    properties: {
                      type: { type: 'keyword' },
                      date: { type: 'date' },
                      description: { type: 'text' },
                      performedBy: { type: 'keyword' }
                    }
                  }
                }
              }
            }
          },

          weather: {
            properties: {
              forecasts: {
                properties: {
                  date: { type: 'date' },
                  temperature: {
                    properties: {
                      min: { type: 'float' },
                      max: { type: 'float' },
                      unit: { type: 'keyword' }
                    }
                  },
                  precipitation: {
                    properties: {
                      amount: { type: 'float' },
                      unit: { type: 'keyword' },
                      type: { type: 'keyword' }
                    }
                  },
                  humidity: { type: 'float' },
                  windSpeed: {
                    properties: {
                      value: { type: 'float' },
                      unit: { type: 'keyword' }
                    }
                  }
                }
              },
              alerts: {
                properties: {
                  type: { type: 'keyword' },
                  severity: { type: 'keyword' },
                  description: { type: 'text' },
                  startDate: { type: 'date' },
                  endDate: { type: 'date' }
                }
              }
            }
          },

          market: {
            properties: {
              demandLevel: { type: 'keyword' },
              priceVolatility: { type: 'keyword' },
              seasonalDemand: { type: 'keyword' },
              targetMarkets: { type: 'keyword' },
              competitionLevel: { type: 'keyword' }
            }
          },

          farmerExperienceLevel: { type: 'keyword' },
          farmerCertification: { type: 'keyword' },
          farmerReputationScore: { type: 'float' },

          ownershipStatus: { type: 'keyword' },
          paymentMethods: { type: 'keyword' },
          pricePerOwnershipUnit: { type: 'double' },
          paymentTerms: { type: 'keyword' },
          cancellationPolicy: { type: 'keyword' },
          agreementDocumentUrl: { type: 'keyword' },
          taxInformation: { type: 'keyword' },

          listingCreationDate: { type: 'date' },
          listingExpiryDate: { type: 'date' },
          viewCount: { type: 'integer' },
          wishlistCount: { type: 'integer' },

          farmId: { type: 'keyword' },
          plotId: { type: 'keyword' },

          serviceArea: { type: 'keyword' },
          serviceDuration: { type: 'keyword' },
          equipmentUsed: { type: 'keyword' },

          brand: { type: 'keyword' },
          model: { type: 'keyword' },
          condition: { type: 'keyword' },
          expiryDate: { type: 'date' }
        }
      },
      certifications: { type: 'keyword' },
      createdAt: { type: 'date' },
      updatedAt: { type: 'date' },
      lastSyncedAt: { type: 'date' }
    }
  }
};

/**
 * Create index configuration for products
 */
export const productIndexConfig = {
  settings: {
    number_of_shards: 3,
    number_of_replicas: 1,
    analysis: {
      analyzer: {
        product_analyzer: {
          type: 'custom' as const,
          tokenizer: 'standard',
          filter: ['lowercase', 'asciifolding']
        }
      }
    }
  }
};

export class ElasticsearchClient {
  private static instance: ElasticsearchClient;
  private client: Client;

  private constructor() {
    this.client = new Client({
      node: ELASTICSEARCH_NODE,
      auth: {
        username: process.env.ELASTICSEARCH_USERNAME || 'elastic',
        password: process.env.ELASTICSEARCH_PASSWORD || ''
      },
      tls: {
        rejectUnauthorized: false
      },
      requestTimeout: 30000,
      pingTimeout: 3000,
      maxRetries: 3
    });
  }

  public static getInstance(): ElasticsearchClient {
    if (!ElasticsearchClient.instance) {
      ElasticsearchClient.instance = new ElasticsearchClient();
    }
    return ElasticsearchClient.instance;
  }

  public getClient(): Client {
    return this.client;
  }

  public async ping(): Promise<boolean> {
    try {
      await this.client.ping();
      return true;
    } catch (error) {
      logger.error('Elasticsearch ping failed:', error);
      return false;
    }
  }

  public async initializeProductIndex(): Promise<boolean> {
    try {
      const exists = await this.client.indices.exists({
        index: PRODUCT_INDEX
      });

      if (!exists) {
        // Create index with settings and basic mapping
        await this.client.indices.create({
          index: PRODUCT_INDEX,
          settings: {
            number_of_shards: 3,
            number_of_replicas: 1,
            analysis: {
              analyzer: {
                product_analyzer: {
                  type: 'custom' as const,
                  tokenizer: 'standard',
                  filter: ['lowercase', 'asciifolding']
                }
              }
            }
          },
          mappings: {
            properties: {
              id: { type: 'keyword' },
              type: { 
                type: 'text',
                fields: {
                  keyword: { type: 'keyword' }
                }
              },
              sellerId: { type: 'keyword' },
              name: {
                type: 'text',
                fields: {
                  keyword: { type: 'keyword' }
                }
              },
              description: { type: 'text' },
              category: { type: 'keyword' },
              subCategory: { type: 'keyword' },
              price: {
                properties: {
                  amount: { type: 'double' },
                  currency: { type: 'keyword' },
                  unit: { type: 'keyword' }
                }
              },
              availability: {
                properties: {
                  status: { type: 'keyword' },
                  quantity: { type: 'double' }
                }
              },
              attributes: {
                properties: {
                  images: { type: 'keyword' },
                  tags: { type: 'keyword' },
                  rating: { type: 'float' },
                  
                  size: { type: 'double' },
                  location: {
                    properties: {
                      country: { type: 'keyword' },
                      state: { type: 'keyword' },
                      city: { type: 'keyword' },
                      pincode: { type: 'keyword' },
                      addressLine1: { type: 'text' },
                      addressLine2: { type: 'text' },
                      latitude: { type: 'float' },
                      longitude: { type: 'float' }
                    }
                  },
                  soilType: { type: 'keyword' },
                  waterAvailability: { type: 'keyword' },
                  
                  cropId: { type: 'keyword' },
                  cropCategory: { type: 'keyword' },
                  variety: { type: 'keyword' },
                  farmingMethod: { type: 'keyword' },
                  growthStage: { type: 'keyword' },
                  irrigationMethod: { type: 'keyword' },
                  harvestSeason: { type: 'keyword' },
                  ownershipUnit: { type: 'keyword' },
                  pestDiseaseStatus: { type: 'keyword' },
                  storageMethod: { type: 'keyword' },
                  nutrientManagement: { type: 'keyword' },
                  waterSource: { type: 'keyword' },
                  pesticideUsage: { type: 'keyword' },
                  seedType: { type: 'keyword' },
                  cropHealthStatus: { type: 'keyword' },
                  harvestingMethod: { type: 'keyword' },
                  packagingType: { type: 'keyword' },
                  transportationOptions: { type: 'keyword' },
                  estimatedDeliveryTime: { type: 'keyword' },
                  deliveryCharges: { type: 'double' },
                  fulfillmentStatus: { type: 'keyword' },
                  riskFactors: { type: 'keyword' },

                  plantingDate: { type: 'date' },
                  expectedHarvestDate: { type: 'date' },
                  actualHarvestDate: { type: 'date' },

                  health: {
                    properties: {
                      status: { type: 'keyword' },
                      issues: { type: 'keyword' },
                      lastCheck: { type: 'date' }
                    }
                  },

                  yield: {
                    properties: {
                      expected: { type: 'double' },
                      actual: { type: 'double' },
                      unit: { type: 'keyword' }
                    }
                  },

                  resources: {
                    properties: {
                      water: { type: 'double' },
                      fertilizer: { type: 'double' },
                      pesticides: { type: 'double' }
                    }
                  },

                  soilConditions: {
                    properties: {
                      type: { type: 'keyword' },
                      ph: { type: 'float' },
                      nutrients: { type: 'keyword' },
                      organicMatter: { type: 'float' },
                      drainage: { type: 'keyword' },
                      salinity: { type: 'keyword' }
                    }
                  },

                  nutritionalInfo: {
                    properties: {
                      calories: { type: 'integer' },
                      protein: { type: 'float' },
                      carbs: { type: 'float' },
                      fiber: { type: 'float' },
                      vitamins: { type: 'keyword' },
                      minerals: { type: 'keyword' },
                      antioxidants: { type: 'keyword' }
                    }
                  },

                  cultivation: {
                    properties: {
                      irrigationNeeds: { type: 'keyword' },
                      fertilizerRequirements: { type: 'keyword' },
                      pestControl: { type: 'keyword' },
                      climateConditions: { type: 'keyword' },
                      sowingMethod: { type: 'keyword' },
                      spacingRequirements: { type: 'keyword' },
                      companionCrops: { type: 'keyword' }
                    }
                  },

                  postHarvest: {
                    properties: {
                      storageRequirements: { type: 'keyword' },
                      shelfLife: { type: 'keyword' },
                      processingMethods: { type: 'keyword' },
                      valueAddedProducts: { type: 'keyword' },
                      marketDemand: { type: 'keyword' },
                      exportPotential: { type: 'boolean' }
                    }
                  },

                  sustainability: {
                    properties: {
                      waterUsage: { type: 'keyword' },
                      carbonFootprint: { type: 'keyword' },
                      pesticide: { type: 'keyword' },
                      biodiversityImpact: { type: 'keyword' },
                      soilHealth: { type: 'keyword' },
                      renewableEnergy: { type: 'boolean' }
                    }
                  },

                  maintenance: {
                    properties: {
                      schedule: {
                        properties: {
                          irrigation: { type: 'date' },
                          fertilization: { type: 'date' },
                          pestControl: { type: 'date' },
                          inspection: { type: 'date' }
                        }
                      },
                      history: {
                        properties: {
                          activities: {
                            properties: {
                              type: { type: 'keyword' },
                              date: { type: 'date' },
                              description: { type: 'text' },
                              performedBy: { type: 'keyword' }
                            }
                          }
                        }
                      }
                    }
                  },

                  weather: {
                    properties: {
                      forecasts: {
                        properties: {
                          date: { type: 'date' },
                          temperature: {
                            properties: {
                              min: { type: 'float' },
                              max: { type: 'float' },
                              unit: { type: 'keyword' }
                            }
                          },
                          precipitation: {
                            properties: {
                              amount: { type: 'float' },
                              unit: { type: 'keyword' },
                              type: { type: 'keyword' }
                            }
                          },
                          humidity: { type: 'float' },
                          windSpeed: {
                            properties: {
                              value: { type: 'float' },
                              unit: { type: 'keyword' }
                            }
                          }
                        }
                      },
                      alerts: {
                        properties: {
                          type: { type: 'keyword' },
                          severity: { type: 'keyword' },
                          description: { type: 'text' },
                          startDate: { type: 'date' },
                          endDate: { type: 'date' }
                        }
                      }
                    }
                  },

                  market: {
                    properties: {
                      demandLevel: { type: 'keyword' },
                      priceVolatility: { type: 'keyword' },
                      seasonalDemand: { type: 'keyword' },
                      targetMarkets: { type: 'keyword' },
                      competitionLevel: { type: 'keyword' }
                    }
                  },

                  farmerExperienceLevel: { type: 'keyword' },
                  farmerCertification: { type: 'keyword' },
                  farmerReputationScore: { type: 'float' },

                  ownershipStatus: { type: 'keyword' },
                  paymentMethods: { type: 'keyword' },
                  pricePerOwnershipUnit: { type: 'double' },
                  paymentTerms: { type: 'keyword' },
                  cancellationPolicy: { type: 'keyword' },
                  agreementDocumentUrl: { type: 'keyword' },
                  taxInformation: { type: 'keyword' },

                  listingCreationDate: { type: 'date' },
                  listingExpiryDate: { type: 'date' },
                  viewCount: { type: 'integer' },
                  wishlistCount: { type: 'integer' },

                  farmId: { type: 'keyword' },
                  plotId: { type: 'keyword' },

                  serviceArea: { type: 'keyword' },
                  serviceDuration: { type: 'keyword' },
                  equipmentUsed: { type: 'keyword' },

                  brand: { type: 'keyword' },
                  model: { type: 'keyword' },
                  condition: { type: 'keyword' },
                  expiryDate: { type: 'date' }
                }
              },
              certifications: { type: 'keyword' },
              createdAt: { type: 'date' },
              updatedAt: { type: 'date' },
              lastSyncedAt: { type: 'date' }
            }
          }
        });
        logger.info('Product index created with mapping');
      } else {
        // Update mapping for existing index
        await this.client.indices.putMapping({
          index: PRODUCT_INDEX,
          properties: {
            id: { type: 'keyword' },
            type: { 
              type: 'text',
              fields: {
                keyword: { type: 'keyword' }
              }
            },
            sellerId: { type: 'keyword' },
            name: {
              type: 'text',
              fields: {
                keyword: { type: 'keyword' }
              }
            },
            description: { type: 'text' },
            category: { type: 'keyword' },
            subCategory: { type: 'keyword' },
            price: {
              properties: {
                amount: { type: 'double' },
                currency: { type: 'keyword' },
                unit: { type: 'keyword' }
              }
            },
            availability: {
              properties: {
                status: { type: 'keyword' },
                quantity: { type: 'double' }
              }
            },
            attributes: {
              properties: {
                images: { type: 'keyword' },
                tags: { type: 'keyword' },
                rating: { type: 'float' },
                size: { type: 'double' },
                location: {
                  properties: {
                    country: { type: 'keyword' },
                    state: { type: 'keyword' },
                    city: { type: 'keyword' },
                    pincode: { type: 'keyword' },
                    addressLine1: { type: 'text' },
                    addressLine2: { type: 'text' },
                    latitude: { type: 'float' },
                    longitude: { type: 'float' }
                  }
                },
                soilType: { type: 'keyword' },
                waterAvailability: { type: 'keyword' },
                cropId: { type: 'keyword' },
                cropCategory: { type: 'keyword' },
                variety: { type: 'keyword' },
                farmingMethod: { type: 'keyword' },
                growthStage: { type: 'keyword' },
                irrigationMethod: { type: 'keyword' },
                harvestSeason: { type: 'keyword' },
                ownershipUnit: { type: 'keyword' },
                pestDiseaseStatus: { type: 'keyword' },
                storageMethod: { type: 'keyword' },
                nutrientManagement: { type: 'keyword' },
                waterSource: { type: 'keyword' },
                pesticideUsage: { type: 'keyword' },
                seedType: { type: 'keyword' },
                cropHealthStatus: { type: 'keyword' },
                harvestingMethod: { type: 'keyword' },
                packagingType: { type: 'keyword' },
                transportationOptions: { type: 'keyword' },
                estimatedDeliveryTime: { type: 'keyword' },
                deliveryCharges: { type: 'double' },
                fulfillmentStatus: { type: 'keyword' },
                riskFactors: { type: 'keyword' },
                plantingDate: { type: 'date' },
                expectedHarvestDate: { type: 'date' },
                actualHarvestDate: { type: 'date' },
                health: {
                  properties: {
                    status: { type: 'keyword' },
                    issues: { type: 'keyword' },
                    lastCheck: { type: 'date' }
                  }
                },
                yield: {
                  properties: {
                    expected: { type: 'double' },
                    actual: { type: 'double' },
                    unit: { type: 'keyword' }
                  }
                },
                resources: {
                  properties: {
                    water: { type: 'double' },
                    fertilizer: { type: 'double' },
                    pesticides: { type: 'double' }
                  }
                },
                soilConditions: {
                  properties: {
                    type: { type: 'keyword' },
                    ph: { type: 'float' },
                    nutrients: { type: 'keyword' },
                    organicMatter: { type: 'float' },
                    drainage: { type: 'keyword' },
                    salinity: { type: 'keyword' }
                  }
                },
                nutritionalInfo: {
                  properties: {
                    calories: { type: 'integer' },
                    protein: { type: 'float' },
                    carbs: { type: 'float' },
                    fiber: { type: 'float' },
                    vitamins: { type: 'keyword' },
                    minerals: { type: 'keyword' },
                    antioxidants: { type: 'keyword' }
                  }
                },
                cultivation: {
                  properties: {
                    irrigationNeeds: { type: 'keyword' },
                    fertilizerRequirements: { type: 'keyword' },
                    pestControl: { type: 'keyword' },
                    climateConditions: { type: 'keyword' },
                    sowingMethod: { type: 'keyword' },
                    spacingRequirements: { type: 'keyword' },
                    companionCrops: { type: 'keyword' }
                  }
                },
                postHarvest: {
                  properties: {
                    storageRequirements: { type: 'keyword' },
                    shelfLife: { type: 'keyword' },
                    processingMethods: { type: 'keyword' },
                    valueAddedProducts: { type: 'keyword' },
                    marketDemand: { type: 'keyword' },
                    exportPotential: { type: 'boolean' }
                  }
                },
                sustainability: {
                  properties: {
                    waterUsage: { type: 'keyword' },
                    carbonFootprint: { type: 'keyword' },
                    pesticide: { type: 'keyword' },
                    biodiversityImpact: { type: 'keyword' },
                    soilHealth: { type: 'keyword' },
                    renewableEnergy: { type: 'boolean' }
                  }
                },
                maintenance: {
                  properties: {
                    schedule: {
                      properties: {
                        irrigation: { type: 'date' },
                        fertilization: { type: 'date' },
                        pestControl: { type: 'date' },
                        inspection: { type: 'date' }
                      }
                    },
                    history: {
                      properties: {
                        activities: {
                          properties: {
                            type: { type: 'keyword' },
                            date: { type: 'date' },
                            description: { type: 'text' },
                            performedBy: { type: 'keyword' }
                          }
                        }
                      }
                    }
                  }
                },
                weather: {
                  properties: {
                    forecasts: {
                      properties: {
                        date: { type: 'date' },
                        temperature: {
                          properties: {
                            min: { type: 'float' },
                            max: { type: 'float' },
                            unit: { type: 'keyword' }
                          }
                        },
                        precipitation: {
                          properties: {
                            amount: { type: 'float' },
                            unit: { type: 'keyword' },
                            type: { type: 'keyword' }
                          }
                        },
                        humidity: { type: 'float' },
                        windSpeed: {
                          properties: {
                            value: { type: 'float' },
                            unit: { type: 'keyword' }
                          }
                        }
                      }
                    },
                    alerts: {
                      properties: {
                        type: { type: 'keyword' },
                        severity: { type: 'keyword' },
                        description: { type: 'text' },
                        startDate: { type: 'date' },
                        endDate: { type: 'date' }
                      }
                    }
                  }
                },
                market: {
                  properties: {
                    demandLevel: { type: 'keyword' },
                    priceVolatility: { type: 'keyword' },
                    seasonalDemand: { type: 'keyword' },
                    targetMarkets: { type: 'keyword' },
                    competitionLevel: { type: 'keyword' }
                  }
                },
                farmerExperienceLevel: { type: 'keyword' },
                farmerCertification: { type: 'keyword' },
                farmerReputationScore: { type: 'float' },
                ownershipStatus: { type: 'keyword' },
                paymentMethods: { type: 'keyword' },
                pricePerOwnershipUnit: { type: 'double' },
                paymentTerms: { type: 'keyword' },
                cancellationPolicy: { type: 'keyword' },
                agreementDocumentUrl: { type: 'keyword' },
                taxInformation: { type: 'keyword' },
                listingCreationDate: { type: 'date' },
                listingExpiryDate: { type: 'date' },
                viewCount: { type: 'integer' },
                wishlistCount: { type: 'integer' },
                farmId: { type: 'keyword' },
                plotId: { type: 'keyword' },
                serviceArea: { type: 'keyword' },
                serviceDuration: { type: 'keyword' },
                equipmentUsed: { type: 'keyword' },
                brand: { type: 'keyword' },
                model: { type: 'keyword' },
                condition: { type: 'keyword' },
                expiryDate: { type: 'date' }
              }
            },
            certifications: { type: 'keyword' },
            createdAt: { type: 'date' },
            updatedAt: { type: 'date' },
            lastSyncedAt: { type: 'date' }
          }
        });
        logger.info('Product index mapping updated');
      }
      return true;
    } catch (error) {
      logger.error('Failed to initialize product index:', error);
      return false;
    }
  }
}

export default ElasticsearchClient;
