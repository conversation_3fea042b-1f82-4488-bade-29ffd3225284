import { Client } from '@elastic/elasticsearch';
import { config } from '../config';
import { logger } from '../utils/logger';
import { ProductDocument } from '../models/product.model';
import { createError, HttpError } from '../utils/error.handler';
import { IndicesCreateRequest } from '@elastic/elasticsearch/lib/api/types';

export class ElasticsearchService {
  private static instance: ElasticsearchService;
  private client: Client;
  private readonly indexName = 'products';

  private constructor() {
    this.client = new Client({
      node: process.env.ELASTICSEARCH_NODE || 'http://localhost:9200',
      auth: {
        username: process.env.ELASTICSEARCH_USERNAME || 'elastic',
        password: process.env.ELASTICSEARCH_PASSWORD || ''
      },
      tls: {
        rejectUnauthorized: false
      },
      requestTimeout: 30000,
      pingTimeout: 3000,
      maxRetries: 3
    });
  }

  public static getInstance(): ElasticsearchService {
    if (!ElasticsearchService.instance) {
      ElasticsearchService.instance = new ElasticsearchService();
    }
    return ElasticsearchService.instance;
  }

  public static async initialize(): Promise<void> {
    const instance = ElasticsearchService.getInstance();
    try {
      await instance.client.ping();
      logger.info('Connected to Elasticsearch');
    } catch (error) {
      logger.error('Failed to initialize Elasticsearch', { error });
      throw new HttpError('Failed to initialize Elasticsearch', 500);
    }
  }

  public static async shutdown(): Promise<void> {
    const instance = ElasticsearchService.getInstance();
    try {
      await instance.client.close();
      logger.info('Elasticsearch connection closed');
    } catch (error) {
      logger.error('Failed to close Elasticsearch connection', { error });
      throw new HttpError('Failed to close Elasticsearch connection', 500);
    }
  }

  private async createIndexIfNotExists(): Promise<void> {
    try {
      const indexExists = await this.client.indices.exists({
        index: this.indexName
      });

      if (!indexExists) {
        const createIndexRequest: IndicesCreateRequest = {
          index: this.indexName,
          settings: {
            analysis: {
              analyzer: {
                custom_analyzer: {
                  type: 'custom',
                  tokenizer: 'standard',
                  filter: ['lowercase', 'stop', 'snowball']
                }
              }
            }
          },
          mappings: {
            properties: {
              name: {
                type: 'text',
                analyzer: 'custom_analyzer',
                fields: {
                  keyword: {
                    type: 'keyword'
                  }
                }
              },
              description: {
                type: 'text',
                analyzer: 'custom_analyzer'
              },
              category: {
                type: 'keyword'
              },
              price: {
                type: 'double'
              },
              availability: {
                type: 'object',
                properties: {
                  status: { type: 'keyword' },
                  quantity: { type: 'integer' }
                }
              },
              attributes: {
                type: 'object',
                dynamic: true
              },
              tags: {
                type: 'keyword'
              },
              createdAt: {
                type: 'date'
              },
              updatedAt: {
                type: 'date'
              }
            }
          }
        };

        await this.client.indices.create(createIndexRequest);
        logger.info('Created Elasticsearch index', { index: this.indexName });
      }
    } catch (error) {
      logger.error('Failed to create Elasticsearch index', { error });
      throw createError('Failed to create Elasticsearch index', 500, 'ELASTICSEARCH_ERROR');
    }
  }

  public async indexProduct(product: ProductDocument): Promise<void> {
    try {
      await this.client.index({
        index: this.indexName,
        id: product._id.toString(),
        document: {
          name: product.name,
          description: product.description,
          category: product.category,
          price: product.price,
          availability: product.availability,
          attributes: product.attributes,
          createdAt: product.createdAt,
          updatedAt: product.updatedAt
        }
      });
      logger.info('Indexed product', { productId: product._id.toString() });
    } catch (error) {
      logger.error('Failed to index product', { error, productId: product._id });
      throw createError('Failed to index product', 500, 'ELASTICSEARCH_ERROR');
    }
  }

  public async updateProduct(product: ProductDocument): Promise<void> {
    try {
      await this.client.update({
        index: this.indexName,
        id: product._id.toString(),
        doc: {
          name: product.name,
          description: product.description,
          category: product.category,
          price: product.price,
          availability: product.availability,
          attributes: product.attributes,
          updatedAt: product.updatedAt
        }
      });
      logger.info('Updated product in Elasticsearch', { productId: product._id.toString() });
    } catch (error) {
      logger.error('Failed to update product in Elasticsearch', { error, productId: product._id });
      throw createError('Failed to update product in Elasticsearch', 500, 'ELASTICSEARCH_ERROR');
    }
  }

  public async deleteProduct(productId: string): Promise<void> {
    try {
      await this.client.delete({
        index: this.indexName,
        id: productId
      });
      logger.info('Deleted product from Elasticsearch', { productId });
    } catch (error) {
      logger.error('Failed to delete product from Elasticsearch', { error, productId });
      throw createError('Failed to delete product from Elasticsearch', 500, 'ELASTICSEARCH_ERROR');
    }
  }

  public async search(query: string, options: SearchOptions = {}): Promise<SearchResult> {
    try {
      const { from = 0, size = 10, sort, filters } = options;

      const searchQuery = this.buildSearchQuery(query, filters);
      const response = await this.client.search({
        index: this.indexName,
        from,
        size,
        sort,
        query: searchQuery,
        aggs: this.buildAggregations()
      });

      return {
        hits: response.hits.hits.map(hit => ({
          id: hit._id,
          score: hit._score || 0,
          document: hit._source
        })),
        total: typeof response.hits.total === 'number' ? response.hits.total : (response.hits.total as any).value,
        aggregations: response.aggregations
      };
    } catch (error) {
      logger.error('Failed to search products', { error, query });
      throw createError('Failed to search products', 500, 'ELASTICSEARCH_ERROR');
    }
  }

  private buildSearchQuery(query: string, filters?: SearchFilters) {
    const must: any[] = [];

    // Full text search
    if (query) {
      must.push({
        multi_match: {
          query,
          fields: ['name^2', 'description', 'tags'],
          type: 'best_fields',
          fuzziness: 'AUTO'
        }
      });
    }

    // Apply filters
    if (filters) {
      if (filters.category) {
        must.push({ term: { category: filters.category } });
      }
      if (filters.minPrice !== undefined || filters.maxPrice !== undefined) {
        const range: any = {};
        if (filters.minPrice !== undefined) range.gte = filters.minPrice;
        if (filters.maxPrice !== undefined) range.lte = filters.maxPrice;
        must.push({ range: { price: range } });
      }
      if (filters.availability) {
        must.push({ term: { 'availability.status': filters.availability } });
      }
      if (filters.attributes) {
        Object.entries(filters.attributes).forEach(([key, value]) => {
          must.push({ term: { [`attributes.${key}`]: value } });
        });
      }
    }

    return {
      bool: {
        must
      }
    };
  }

  private buildAggregations() {
    return {
      categories: {
        terms: {
          field: 'category'
        }
      },
      price_ranges: {
        range: {
          field: 'price',
          ranges: [
            { to: 50 },
            { from: 50, to: 100 },
            { from: 100, to: 200 },
            { from: 200 }
          ]
        }
      },
      availability: {
        terms: {
          field: 'availability.status'
        }
      }
    };
  }

  /**
   * Get Elasticsearch cluster health
   */
  public async getHealth(): Promise<{ status: string }> {
    try {
      const health = await this.client.cluster.health();
      return { status: health.status };
    } catch (error) {
      logger.error('Failed to get Elasticsearch health', { error });
      throw new HttpError('Failed to get Elasticsearch health', 500);
    }
  }
}

interface SearchOptions {
  from?: number;
  size?: number;
  sort?: any;
  filters?: SearchFilters;
}

interface SearchFilters {
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  availability?: string;
  attributes?: Record<string, any>;
}

interface SearchResult {
  hits: Array<{
    id: string;
    score: number;
    document: any;
  }>;
  total: number;
  aggregations?: any;
}
