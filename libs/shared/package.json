{"name": "@agritech/shared", "version": "1.0.0", "description": "Shared utilities and types for AgriTech microservices", "main": "./src/index.js", "types": "./src/index.d.ts", "type": "commonjs", "scripts": {"build": "nx build shared", "test": "nx test shared", "test:coverage": "nx test shared --coverage", "lint": "nx lint shared", "format": "nx format:write shared"}, "dependencies": {"@agritech/types": "1.0.0", "aws-sdk": "2.1550.0", "dotenv": "16.4.5", "jsonwebtoken": "9.0.2", "nodemailer": "6.9.12", "redis": "4.6.13", "stripe": "14.20.0", "mongoose": "^8.14.1"}, "devDependencies": {"@types/aws-sdk": "2.7.0", "@types/jsonwebtoken": "9.0.6", "@types/node": "20.11.24", "@types/nodemailer": "6.4.14", "@typescript-eslint/eslint-plugin": "7.1.0", "@typescript-eslint/parser": "7.1.0", "@vitest/coverage-v8": "1.3.1", "eslint": "8.57.0", "prettier": "3.2.5", "typescript": "5.3.3", "vitest": "1.3.1"}, "nx": {"targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/shared", "main": "src/index.ts", "tsConfig": "tsconfig.json", "assets": ["src/**/*.json"]}}, "test": {"executor": "@nx/vite:test", "outputs": ["{options.reportsDirectory}"], "options": {"passWithNoTests": true}}, "lint": {"executor": "@nx/eslint:lint"}}}}