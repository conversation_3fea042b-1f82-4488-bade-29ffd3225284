# AgriTech Shared Library

A shared library containing common types, utilities, and configuration for the AgriTech platform.

## Features

- Common TypeScript interfaces and types
- Environment configuration management
- JWT authentication utilities
- Date formatting and pagination helpers
- Order and product calculation utilities

## Installation

```bash
npm install @agritech/shared
```

## Usage

### Types

```typescript
import { User, Product, Order, UserRole } from '@agritech/shared';

const user: User = {
  id: '1',
  email: '<EMAIL>',
  name: '<PERSON>',
  role: UserRole.BUYER,
  createdAt: new Date(),
  updatedAt: new Date(),
};
```

### Configuration

```typescript
import { getConfig, isDevelopment } from '@agritech/shared';

const config = getConfig();
if (isDevelopment()) {
  // Development-specific code
}
```

### Utilities

```typescript
import {
  generateToken,
  verifyToken,
  calculatePagination,
  generateOrderNumber,
} from '@agritech/shared';

// Generate JWT token
const token = generateToken(user);

// Calculate pagination
const { skip, limit, totalPages } = calculatePagination(1, 10, 100);

// Generate order number
const orderNumber = generateOrderNumber();
```

## Environment Variables

The following environment variables are required:

- `PORT`: Server port number
- `NODE_ENV`: Environment (development, production, test)
- `MONGODB_URI`: MongoDB connection string
- `JWT_SECRET`: Secret key for JWT
- `JWT_EXPIRES_IN`: JWT token expiration time
- `CORS_ORIGIN`: CORS allowed origin

## License

MIT
