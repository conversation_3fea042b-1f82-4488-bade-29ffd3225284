import { config } from 'dotenv';
import * as path from 'path';

// Load environment variables
config({ path: path.resolve(process.cwd(), '.env') });

export interface AppConfig {
  // Server
  port: number;
  nodeEnv: string;
  corsOrigin: string;
  
  // Database
  mongoUri: string;
  
  // Authentication
  jwtSecret: string;
  jwtExpiresIn: string;
  
  // Email
  smtpHost: string;
  smtpPort: number;
  smtpUser: string;
  smtpPass: string;
  emailFrom: string;
  
  // Storage
  awsAccessKeyId: string;
  awsSecretAccessKey: string;
  awsRegion: string;
  awsBucketName: string;
  
  // Payment
  stripeSecretKey: string;
  stripeWebhookSecret: string;
  
  // Redis
  redisUrl: string;
  
  // Service URLs
  authServiceUrl: string;
  productServiceUrl: string;
  orderServiceUrl: string;
  notificationServiceUrl: string;
  deliveryServiceUrl: string;

  // Kafka
  kafkaBrokers: string;
}

export const getConfig = (): AppConfig => {
  const requiredEnvVars = [
    // Server
    'PORT',
    'NODE_ENV',
    'CORS_ORIGIN',
    
    // Database
    'MONGODB_URI',
    
    // Authentication
    'JWT_SECRET',
    'JWT_EXPIRES_IN',
    
    // Email
    'SMTP_HOST',
    'SMTP_PORT',
    'SMTP_USER',
    'SMTP_PASS',
    'EMAIL_FROM',
    
    // Storage
    'AWS_ACCESS_KEY_ID',
    'AWS_SECRET_ACCESS_KEY',
    'AWS_REGION',
    'AWS_BUCKET_NAME',
    
    // Payment
    'STRIPE_SECRET_KEY',
    'STRIPE_WEBHOOK_SECRET',
    
    // Redis
    'REDIS_URL',
    
    // Service URLs
    'AUTH_SERVICE_URL',
    'PRODUCT_SERVICE_URL',
    'ORDER_SERVICE_URL',
    'NOTIFICATION_SERVICE_URL',
    'DELIVERY_SERVICE_URL',

    // Kafka
    'KAFKA_BROKERS',
  ];

  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`Missing required environment variable: ${envVar}`);
    }
  }

  return {
    // Server
    port: parseInt(process.env['PORT']!, 10),
    nodeEnv: process.env['NODE_ENV']!,
    corsOrigin: process.env['CORS_ORIGIN']!,
    
    // Database
    mongoUri: process.env['MONGODB_URI']!,
    
    // Authentication
    jwtSecret: process.env['JWT_SECRET']!,
    jwtExpiresIn: process.env['JWT_EXPIRES_IN']!,
    
    // Email
    smtpHost: process.env['SMTP_HOST']!,
    smtpPort: parseInt(process.env['SMTP_PORT']!, 10),
    smtpUser: process.env['SMTP_USER']!,
    smtpPass: process.env['SMTP_PASS']!,
    emailFrom: process.env['EMAIL_FROM']!,
    
    // Storage
    awsAccessKeyId: process.env['AWS_ACCESS_KEY_ID']!,
    awsSecretAccessKey: process.env['AWS_SECRET_ACCESS_KEY']!,
    awsRegion: process.env['AWS_REGION']!,
    awsBucketName: process.env['AWS_BUCKET_NAME']!,
    
    // Payment
    stripeSecretKey: process.env['STRIPE_SECRET_KEY']!,
    stripeWebhookSecret: process.env['STRIPE_WEBHOOK_SECRET']!,
    
    // Redis
    redisUrl: process.env['REDIS_URL']!,
    
    // Service URLs
    authServiceUrl: process.env['AUTH_SERVICE_URL']!,
    productServiceUrl: process.env['PRODUCT_SERVICE_URL']!,
    orderServiceUrl: process.env['ORDER_SERVICE_URL']!,
    notificationServiceUrl: process.env['NOTIFICATION_SERVICE_URL']!,
    deliveryServiceUrl: process.env['DELIVERY_SERVICE_URL']!,

    // Kafka
    kafkaBrokers: process.env['KAFKA_BROKERS']!,
  };
};

export const isDevelopment = (): boolean => {
  return process.env['NODE_ENV'] === 'development';
};

export const isProduction = (): boolean => {
  return process.env['NODE_ENV'] === 'production';
};

export const isTest = (): boolean => {
  return process.env['NODE_ENV'] === 'test';
}; 