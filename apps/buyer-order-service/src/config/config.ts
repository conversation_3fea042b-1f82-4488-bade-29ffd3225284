import dotenv from 'dotenv';
dotenv.config(); // Load .env file

// Basic configuration structure
// Replace placeholders with actual values or environment variables
export const config = {
  port: process.env.ORDER_SERVICE_PORT || 3335, // Changed port env variable and default
  mongoUri: process.env.MONGODB_URI,
  cors: {
    origin: process.env.CORS_ORIGIN || '*', // Adjust for production
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: true,
  },
  // Add Kafka configuration (brokers, topic names) - Placeholder
  kafka: {
    brokers: (process.env.KAFKA_BROKERS || 'localhost:9092').split(','),
    clientId: process.env.KAFKA_CLIENT_ID || 'buyer-order-service',
    topics: {
        cartCheckedOut: process.env.KAFKA_TOPIC_CART_CHECKED_OUT || 'cart.checkedout',
        paymentProcessed: process.env.KAFKA_TOPIC_PAYMENT_PROCESSED || 'payment.processed',
        paymentFailed: process.env.KAFKA_TOPIC_PAYMENT_FAILED || 'payment.failed',
        orderCreated: process.env.KAFKA_TOPIC_ORDER_CREATED || 'order.created',
        orderPaid: process.env.KAFKA_TOPIC_ORDER_PAID || 'order.paid',
        orderCancelled: process.env.KAFKA_TOPIC_ORDER_CANCELLED || 'order.cancelled',
        orderShipped: process.env.KAFKA_TOPIC_ORDER_SHIPPED || 'order.shipped',
        orderDelivered: process.env.KAFKA_TOPIC_ORDER_DELIVERED || 'order.delivered',
    },
    groupId: process.env.KAFKA_GROUP_ID_ORDER || 'order-service-group'
  },
  // Add other service-specific configurations if needed
}; 