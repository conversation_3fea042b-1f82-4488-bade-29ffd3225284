import { Client } from '@elastic/elasticsearch';
import { SearchTotalHits, AggregationsAggregate, SearchSuggest, SearchResponse } from '@elastic/elasticsearch/lib/api/types';
import { ProductDocument } from '../config/elasticsearch';
import { ProductType, CropGrowthStage, CropHealthStatus } from '../models/product.model';
import { config } from '../config';
import { logger } from '../utils/logger';

/**
 * Search filters interface
 */
export interface SearchFilters {
  // Basic filters
  productTypes?: ProductType[];
  categories?: string[];
  subCategories?: string[];
  sellerId?: string;
  
  // Price filters
  minPrice?: number;
  maxPrice?: number;
  currency?: string;
  
  // Location filters
  country?: string;
  state?: string;
  city?: string;
  pincode?: string;
  
  // Geo-location filters
  latitude?: number;
  longitude?: number;
  radius?: string; // e.g., "10km"
  
  // Crop-specific filters
  cropCategories?: string[];
  varieties?: string[];
  farmingMethods?: string[];
  harvestSeasons?: string[];
  
  // Availability filters
  availabilityStatus?: ('AVAILABLE' | 'OUT_OF_STOCK' | 'LIMITED')[];
  minQuantity?: number;
  
  // Rating and reputation filters
  minRating?: number;
  minFarmerReputationScore?: number;
  
  // Date filters
  harvestStartDate?: Date;
  harvestEndDate?: Date;
  listingExpiryAfter?: Date;
  
  // Other filters
  certifications?: string[];
  tags?: string[];
  farmingCertifications?: string[];
  
  // Equipment/Supply filters
  brands?: string[];
  conditions?: string[];
}

/**
 * Search options interface
 */
export interface SearchOptions {
  query?: string;
  page?: number;
  limit?: number;
  filters?: {
    minPrice?: number;
    maxPrice?: number;
    sellerId?: string;
    categories?: string[];
    type?: string;
    // Crop-specific filters
    cropCategory?: string;
    farmingMethod?: string;
    growthStage?: string;
    harvestSeason?: string;
    cropHealthStatus?: string;
    expectedHarvestDateStart?: string;
    expectedHarvestDateEnd?: string;
    soilType?: string;
    waterSource?: string;
    pesticideUsage?: string;
    seedType?: string;
    certifications?: string[];
    location?: {
      state?: string;
      city?: string;
      pincode?: string;
    };
  };
  sort?: {
    field: string;
    order: 'asc' | 'desc';
  }[];
}

/**
 * Search result interface
 */
export interface SearchResult {
  products: ProductDocument[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  aggregations?: Record<string, any>;
  took?: number;
}

/**
 * Autocomplete result interface
 */
export interface AutocompleteResult {
  suggestions: AutocompleteSuggestion[];
  total: number;
}

interface AutocompleteSuggestion {
  text: string;
  type: 'product' | 'category' | 'variety' | 'crop_category' | 'farming_method' | 'seed_type' | 'soil_type' | 'water_source';
  count?: number;
}

interface SearchParams {
  page: number;
  limit: number;
  query?: string;
  filters?: Record<string, any>;
  sort?: Array<{ field: string; order: 'asc' | 'desc' }>;
}

interface TermsBucket {
  key: string;
  doc_count: number;
}

interface TermsAggregation {
  buckets: TermsBucket[];
}

interface SearchAggregations {
  categories?: TermsAggregation;
  varieties?: TermsAggregation;
  crop_categories?: TermsAggregation;
  farming_methods?: TermsAggregation;
  seed_types?: TermsAggregation;
  soil_types?: TermsAggregation;
  water_sources?: TermsAggregation;
}

/**
 * Search service for advanced product search capabilities
 */
export class SearchService {
  private client: Client;
  private productIndex: string;

  constructor() {
    const ELASTICSEARCH_NODE = process.env.ELASTICSEARCH_NODE || 'http://localhost:9200';
    const PRODUCT_INDEX = process.env.ELASTICSEARCH_INDEX || 'befarma';

    this.client = new Client({
      node: ELASTICSEARCH_NODE,
      auth: {
        username: process.env.ELASTICSEARCH_USERNAME || 'elastic',
        password: process.env.ELASTICSEARCH_PASSWORD || ''
      },
      tls: {
        rejectUnauthorized: false
      },
      requestTimeout: 30000,
      pingTimeout: 3000,
      maxRetries: 3
    });
    this.productIndex = PRODUCT_INDEX;
  }

  /**
   * Build Elasticsearch query from search options
   */
  private buildQuery(params: SearchParams): Record<string, any> {
    const { query, filters } = params;
    const must: any[] = [];
    const filter: any[] = [];

    // Text search
    if (query && query.trim()) {
      must.push({
        multi_match: {
          query: query.trim(),
          fields: [
            'name^3',
            'description^2',
            'category^2',
            'subCategory',
            'attributes.variety^2',
            'attributes.cropCategory^2',
            'attributes.tags',
            'attributes.farmingMethod^1.5',
            'attributes.harvestSeason^1.5',
            'attributes.soilType^1.2',
            'attributes.waterSource^1.2',
            'attributes.seedType^1.2',
            'attributes.nutritionalInfo.vitamins^1.1',
            'attributes.nutritionalInfo.minerals^1.1',
            'attributes.cultivation.climateConditions^1.1',
            'attributes.postHarvest.processingMethods^1.1',
            'attributes.sustainability.waterUsage^1.1',
            'attributes.sustainability.carbonFootprint^1.1'
          ],
          type: 'best_fields',
          fuzziness: 'AUTO'
        }
      });
    }

    // Apply filters
    if (filters) {
      // Product types
      if (filters.productTypes?.length) {
        filter.push({ terms: { type: filters.productTypes } });
      }

      // Categories
      if (filters.categories?.length) {
        filter.push({ terms: { category: filters.categories } });
      }

      // Sub-categories
      if (filters.subCategories?.length) {
        filter.push({ terms: { subCategory: filters.subCategories } });
      }

      // Seller ID
      if (filters.sellerId) {
        filter.push({ term: { sellerId: filters.sellerId } });
      }

      // Price range
      if (filters.minPrice !== undefined || filters.maxPrice !== undefined) {
        const priceRange: any = {};
        if (filters.minPrice !== undefined) priceRange.gte = filters.minPrice;
        if (filters.maxPrice !== undefined) priceRange.lte = filters.maxPrice;
        filter.push({ range: { 'price.amount': priceRange } });
      }

      // Currency
      if (filters.currency) {
        filter.push({ term: { 'price.currency': filters.currency } });
      }

      // Location filters
      if (filters.country) {
        filter.push({ term: { 'attributes.location.country': filters.country } });
      }
      if (filters.state) {
        filter.push({ term: { 'attributes.location.state': filters.state } });
      }
      if (filters.city) {
        filter.push({ term: { 'attributes.location.city': filters.city } });
      }
      if (filters.pincode) {
        filter.push({ term: { 'attributes.location.pincode': filters.pincode } });
      }

      // Geo-location filter
      if (filters.latitude && filters.longitude && filters.radius) {
        filter.push({
          geo_distance: {
            distance: filters.radius,
            'attributes.location': {
              lat: filters.latitude,
              lon: filters.longitude
            }
          }
        });
      }

      // Crop-specific filters
      if (filters.cropCategories?.length) {
        filter.push({ terms: { 'attributes.cropCategory': filters.cropCategories } });
      }
      if (filters.varieties?.length) {
        filter.push({ terms: { 'attributes.variety': filters.varieties } });
      }
      if (filters.farmingMethods?.length) {
        filter.push({ terms: { 'attributes.farmingMethod': filters.farmingMethods } });
      }
      if (filters.harvestSeasons?.length) {
        filter.push({ terms: { 'attributes.harvestSeason': filters.harvestSeasons } });
      }

      // Growth stage and health status
      if (filters.growthStage) {
        filter.push({ term: { 'attributes.growthStage': filters.growthStage } });
      }
      if (filters.cropHealthStatus) {
        filter.push({ term: { 'attributes.cropHealthStatus': filters.cropHealthStatus } });
      }

      // Harvest date range
      if (filters.harvestStartDate || filters.harvestEndDate) {
        const dateRange: any = {};
        if (filters.harvestStartDate) dateRange.gte = filters.harvestStartDate.toISOString();
        if (filters.harvestEndDate) dateRange.lte = filters.harvestEndDate.toISOString();
        filter.push({ range: { 'attributes.expectedHarvestDate': dateRange } });
      }

      // Soil and water conditions
      if (filters.soilType) {
        filter.push({ term: { 'attributes.soilConditions.type': filters.soilType } });
      }
      if (filters.waterSource) {
        filter.push({ term: { 'attributes.waterSource': filters.waterSource } });
      }

      // Pesticide and seed type
      if (filters.pesticideUsage) {
        filter.push({ term: { 'attributes.pesticideUsage': filters.pesticideUsage } });
      }
      if (filters.seedType) {
        filter.push({ term: { 'attributes.seedType': filters.seedType } });
      }

      // Availability filters
      if (filters.availabilityStatus?.length) {
        filter.push({ terms: { 'availability.status': filters.availabilityStatus } });
      }
      if (filters.minQuantity !== undefined) {
        filter.push({ range: { 'availability.quantity': { gte: filters.minQuantity } } });
      }

      // Rating filters
      if (filters.minRating !== undefined) {
        filter.push({ range: { 'attributes.rating': { gte: filters.minRating } } });
      }
      if (filters.minFarmerReputationScore !== undefined) {
        filter.push({ range: { 'attributes.farmerReputationScore': { gte: filters.minFarmerReputationScore } } });
      }

      // Listing expiry
      if (filters.listingExpiryAfter) {
        filter.push({ 
          range: { 
            'attributes.listingExpiryDate': { 
              gte: filters.listingExpiryAfter.toISOString() 
            } 
          } 
        });
      }

      // Certifications and tags
      if (filters.certifications?.length) {
        filter.push({ terms: { certifications: filters.certifications } });
      }
      if (filters.tags?.length) {
        filter.push({ terms: { 'attributes.tags': filters.tags } });
      }
      if (filters.farmingCertifications?.length) {
        filter.push({ terms: { 'attributes.farmerCertification': filters.farmingCertifications } });
      }

      // Equipment/Supply filters
      if (filters.brands?.length) {
        filter.push({ terms: { 'attributes.brand': filters.brands } });
      }
      if (filters.conditions?.length) {
        filter.push({ terms: { 'attributes.condition': filters.conditions } });
      }
    }

    // Build the final query
    const esQuery: any = {
      bool: {
        must: must.length > 0 ? must : [{ match_all: {} }],
        filter
      }
    };

    return esQuery;
  }

  /**
   * Build sort configuration
   */
  private buildSort(params: SearchParams): Array<Record<string, { order: 'asc' | 'desc' }>> {
    const sort: Array<Record<string, { order: 'asc' | 'desc' }>> = [];

    if (params.sort?.length) {
      params.sort.forEach(({ field, order }) => {
        sort.push({ [field]: { order } });
      });
    }

    return sort;
  }

  /**
   * Build aggregations for faceted search
   */
  private buildAggregations(): Record<string, any> {
    return {
      product_types: {
        terms: { field: 'type.keyword', size: 10 }
      },
      categories: {
        terms: { field: 'category', size: 20 }
      },
      sub_categories: {
        terms: { field: 'subCategory', size: 30 }
      },
      crop_categories: {
        terms: { field: 'attributes.cropCategory', size: 20 }
      },
      varieties: {
        terms: { field: 'attributes.variety', size: 20 }
      },
      farming_methods: {
        terms: { field: 'attributes.farmingMethod', size: 10 }
      },
      harvest_seasons: {
        terms: { field: 'attributes.harvestSeason', size: 10 }
      },
      growth_stages: {
        terms: { field: 'attributes.growthStage', size: 5 }
      },
      health_status: {
        terms: { field: 'attributes.cropHealthStatus', size: 5 }
      },
      soil_types: {
        terms: { field: 'attributes.soilConditions.type', size: 15 }
      },
      water_sources: {
        terms: { field: 'attributes.waterSource', size: 10 }
      },
      pesticide_usage: {
        terms: { field: 'attributes.pesticideUsage', size: 10 }
      },
      seed_types: {
        terms: { field: 'attributes.seedType', size: 10 }
      },
      availability_status: {
        terms: { field: 'availability.status', size: 5 }
      },
      price_ranges: {
        range: {
          field: 'price.amount',
          ranges: [
            { to: 1000 },
            { from: 1000, to: 5000 },
            { from: 5000, to: 10000 },
            { from: 10000, to: 50000 },
            { from: 50000 }
          ]
        }
      },
      expected_harvest_dates: {
        date_histogram: {
          field: 'attributes.expectedHarvestDate',
          calendar_interval: 'month',
          min_doc_count: 1
        }
      },
      states: {
        terms: { field: 'attributes.location.state', size: 30 }
      },
      cities: {
        terms: { field: 'attributes.location.city', size: 50 }
      },
      certifications: {
        terms: { field: 'certifications', size: 20 }
      },
      farming_certifications: {
        terms: { field: 'attributes.farmerCertification', size: 20 }
      },
      rating_ranges: {
        range: {
          field: 'attributes.rating',
          ranges: [
            { from: 4.5, to: 5.0 },
            { from: 4.0, to: 4.5 },
            { from: 3.5, to: 4.0 },
            { from: 3.0, to: 3.5 },
            { to: 3.0 }
          ]
        }
      },
      farmer_reputation_ranges: {
        range: {
          field: 'attributes.farmerReputationScore',
          ranges: [
            { from: 4.5, to: 5.0 },
            { from: 4.0, to: 4.5 },
            { from: 3.5, to: 4.0 },
            { from: 3.0, to: 3.5 },
            { to: 3.0 }
          ]
        }
      }
    };
  }

  /**
   * Get search results with aggregations
   */
  async search(searchParams: SearchParams): Promise<SearchResult> {
    try {
      const response = await this.client.search({
        index: this.productIndex,
        query: this.buildQuery(searchParams),
        sort: this.buildSort(searchParams),
        from: (searchParams.page - 1) * searchParams.limit,
        size: searchParams.limit,
        aggs: this.buildAggregations()
      });

      const hits = response.hits;
      const total = typeof hits.total === 'number' ? hits.total : (hits.total as SearchTotalHits).value;
      const products = hits.hits.map((hit: any) => ({
        ...hit._source,
        _score: hit._score,
        highlight: hit.highlight
      }));

      return {
        products,
        total,
        page: searchParams.page,
        limit: searchParams.limit,
        totalPages: Math.ceil(total / searchParams.limit),
        aggregations: response.aggregations,
        took: response.took
      };
    } catch (error) {
      logger.error('Search failed:', error);
      throw error;
    }
  }

  /**
   * Get autocomplete suggestions
   */
  async getAutocompleteSuggestions(query: string, limit = 10): Promise<AutocompleteResult> {
    try {
      const response = await this.client.search<SearchResponse<unknown>>({
        index: this.productIndex,
        suggest: {
          product_suggest: {
            prefix: query,
            completion: {
              field: 'name.suggest',
              size: limit
            }
          }
        },
        aggs: {
          categories: {
            terms: {
              field: 'category.keyword',
              include: `${query}.*`,
              size: limit
            }
          },
          varieties: {
            terms: {
              field: 'attributes.variety.keyword',
              include: `${query}.*`,
              size: limit
            }
          },
          crop_categories: {
            terms: {
              field: 'attributes.cropCategory.keyword',
              include: `${query}.*`,
              size: limit
            }
          },
          farming_methods: {
            terms: {
              field: 'attributes.farmingMethod.keyword',
              include: `${query}.*`,
              size: limit
            }
          },
          seed_types: {
            terms: {
              field: 'attributes.seedType.keyword',
              include: `${query}.*`,
              size: limit
            }
          },
          soil_types: {
            terms: {
              field: 'attributes.soilConditions.type.keyword',
              include: `${query}.*`,
              size: limit
            }
          },
          water_sources: {
            terms: {
              field: 'attributes.waterSource.keyword',
              include: `${query}.*`,
              size: limit
            }
          }
        }
      });

      const suggestions: AutocompleteSuggestion[] = [];

      // Add product name suggestions
      const productSuggestions = response.suggest?.product_suggest?.[0]?.options;
      if (Array.isArray(productSuggestions)) {
        productSuggestions.forEach((option: any) => {
          suggestions.push({
            text: option.text,
            type: 'product'
          });
        });
      }

      // Add category suggestions
      const aggregations = response.aggregations as SearchAggregations;
      if (aggregations?.categories?.buckets) {
        aggregations.categories.buckets.forEach(bucket => {
          suggestions.push({
            text: bucket.key,
            type: 'category',
            count: bucket.doc_count
          });
        });
      }

      // Add variety suggestions
      if (aggregations?.varieties?.buckets) {
        aggregations.varieties.buckets.forEach(bucket => {
          suggestions.push({
            text: bucket.key,
            type: 'variety',
            count: bucket.doc_count
          });
        });
      }

      // Add crop category suggestions
      if (aggregations?.crop_categories?.buckets) {
        aggregations.crop_categories.buckets.forEach(bucket => {
          suggestions.push({
            text: bucket.key,
            type: 'crop_category',
            count: bucket.doc_count
          });
        });
      }

      // Add farming method suggestions
      if (aggregations?.farming_methods?.buckets) {
        aggregations.farming_methods.buckets.forEach(bucket => {
          suggestions.push({
            text: bucket.key,
            type: 'farming_method',
            count: bucket.doc_count
          });
        });
      }

      // Add seed type suggestions
      if (aggregations?.seed_types?.buckets) {
        aggregations.seed_types.buckets.forEach(bucket => {
          suggestions.push({
            text: bucket.key,
            type: 'seed_type',
            count: bucket.doc_count
          });
        });
      }

      // Add soil type suggestions
      if (aggregations?.soil_types?.buckets) {
        aggregations.soil_types.buckets.forEach(bucket => {
          suggestions.push({
            text: bucket.key,
            type: 'soil_type',
            count: bucket.doc_count
          });
        });
      }

      // Add water source suggestions
      if (aggregations?.water_sources?.buckets) {
        aggregations.water_sources.buckets.forEach(bucket => {
          suggestions.push({
            text: bucket.key,
            type: 'water_source',
            count: bucket.doc_count
          });
        });
      }

      return {
        suggestions: suggestions.slice(0, limit),
        total: suggestions.length
      };
    } catch (error) {
      logger.error('Autocomplete search failed:', error);
      return { suggestions: [], total: 0 };
    }
  }

  /**
   * Get similar products based on product attributes
   */
  async getSimilarProducts(productId: string, limit = 10): Promise<ProductDocument[]> {
    try {
      // First get the source product
      const sourceProduct = await this.client.get({
        index: this.productIndex,
        id: productId
      });

      if (!sourceProduct.found) {
        throw new Error('Product not found');
      }

      const product = sourceProduct._source as ProductDocument;

      // Build more like this query based on product type
      const fields = [
        'name^3',
        'description^2',
        'category^2',
        'subCategory',
        'attributes.tags'
      ];

      // Add crop-specific fields if the product is a crop
      if (product.type === ProductType.CROP) {
        fields.push(
          'attributes.cropCategory^3',
          'attributes.variety^3',
          'attributes.farmingMethod^2',
          'attributes.harvestSeason^2',
          'attributes.soilType^1.5',
          'attributes.waterSource^1.5',
          'attributes.seedType^1.5',
          'attributes.nutritionalInfo.vitamins^1.2',
          'attributes.nutritionalInfo.minerals^1.2',
          'attributes.cultivation.climateConditions^1.2',
          'attributes.postHarvest.processingMethods^1.2',
          'attributes.sustainability.waterUsage^1.2',
          'attributes.sustainability.carbonFootprint^1.2'
        );
      }

      // Build the query
      const response = await this.client.search({
        index: this.productIndex,
        query: {
          bool: {
            must: [
              {
                more_like_this: {
                  fields,
                  like: [
                    {
                      _index: this.productIndex,
                      _id: productId
                    }
                  ],
                  min_term_freq: 1,
                  max_query_terms: 25,
                  min_doc_freq: 1,
                  minimum_should_match: '30%'
                }
              }
            ],
            filter: [
              { term: { type: product.type } },
              { term: { 'availability.status': 'AVAILABLE' } },
              { term: { isActive: true } }
            ],
            must_not: [
              { term: { _id: productId } } // Exclude the source product
            ]
          }
        },
        size: limit,
        _source: true
      });

      return response.hits.hits.map(hit => ({
        ...hit._source as ProductDocument,
        _score: hit._score
      }));
    } catch (error) {
      logger.error('Failed to get similar products:', error);
      return [];
    }
  }

  /**
   * Get trending products based on various factors
   */
  async getTrendingProducts(limit = 20): Promise<ProductDocument[]> {
    try {
      const response = await this.client.search({
        index: this.productIndex,
        query: {
          bool: {
            must: [
              {
                range: {
                  'createdAt': {
                    gte: 'now-30d/d'
                  }
                }
              }
            ],
            filter: [
              { term: { 'availability.status': 'AVAILABLE' } },
              { term: { isActive: true } }
            ]
          }
        },
        sort: [
          {
            _script: {
              type: 'number',
              script: {
                lang: 'painless',
                source: `
                  double score = 0;
                  
                  // Base score from view count and wishlist count
                  score += doc['attributes.viewCount'].value * 0.3;
                  score += doc['attributes.wishlistCount'].value * 0.5;
                  
                  // Boost based on rating
                  if (doc['attributes.rating'].size() > 0) {
                    score += doc['attributes.rating'].value * 10;
                  }
                  
                  // Boost based on farmer reputation
                  if (doc['attributes.farmerReputationScore'].size() > 0) {
                    score += doc['attributes.farmerReputationScore'].value * 5;
                  }
                  
                  // Boost for crops based on harvest readiness
                  if (doc['type'].value == 'CROP') {
                    // Boost for crops near harvest
                    if (doc['attributes.expectedHarvestDate'].size() > 0) {
                      long daysToHarvest = (doc['attributes.expectedHarvestDate'].value.toInstant().toEpochMilli() - System.currentTimeMillis()) / (1000 * 60 * 60 * 24);
                      if (daysToHarvest >= 0 && daysToHarvest <= 30) {
                        score += 50; // High boost for crops harvesting within a month
                      } else if (daysToHarvest > 30 && daysToHarvest <= 90) {
                        score += 30; // Medium boost for crops harvesting within three months
                      }
                    }
                    
                    // Boost for healthy crops
                    if (doc['attributes.cropHealthStatus'].value == 'HEALTHY') {
                      score += 20;
                    }
                    
                    // Boost for organic farming
                    if (doc['attributes.farmingMethod'].value == 'ORGANIC') {
                      score += 15;
                    }
                    
                    // Boost for crops with certifications
                    if (doc['certifications'].size() > 0) {
                      score += doc['certifications'].size() * 5;
                    }
                  }
                  
                  // Recency boost
                  long ageInDays = (System.currentTimeMillis() - doc['createdAt'].value.toInstant().toEpochMilli()) / (1000 * 60 * 60 * 24);
                  score += Math.max(0, 30 - ageInDays) * 2;
                  
                  return score;
                `
              },
              order: 'desc'
            }
          }
        ],
        size: limit,
        _source: true
      });

      return response.hits.hits.map(hit => ({
        ...hit._source as ProductDocument,
        _score: hit._score
      }));
    } catch (error) {
      logger.error('Failed to get trending products:', error);
      return [];
    }
  }
}

export default SearchService;
