import { config } from '../config';
import { logger } from '../utils/logger';
import ElasticsearchClient from '../config/elasticsearch';
import { Client } from '@elastic/elasticsearch';

async function initializeElasticsearch() {
  try {
    // Create a new client with explicit configuration
    const client = new Client({
      node: process.env.ELASTICSEARCH_NODE,
      auth: {
        username: process.env.ELASTICSEARCH_USERNAME,
        password: process.env.ELASTICSEARCH_PASSWORD
      },
      tls: {
        rejectUnauthorized: false
      }
    });

    // Test connection
    const pingResult = await client.ping();
    logger.info('Elasticsearch ping successful');

    // Check if index exists
    const indexExists = await client.indices.exists({
      index: process.env.ELASTICSEARCH_INDEX
    });

    if (!indexExists) {
      // Create index with mapping
      await client.indices.create({
        index: process.env.ELASTICSEARCH_INDEX,
        mappings: {
          properties: {
            id: { type: 'keyword' },
            type: { 
              type: 'text',
              fields: {
                keyword: { type: 'keyword' }
              }
            },
            sellerId: { type: 'keyword' },
            name: {
              type: 'text',
              fields: {
                keyword: { type: 'keyword' }
              }
            },
            description: { type: 'text' },
            category: { type: 'keyword' },
            subCategory: { type: 'keyword' },
            price: {
              properties: {
                amount: { type: 'double' },
                currency: { type: 'keyword' },
                unit: { type: 'keyword' }
              }
            },
            availability: {
              properties: {
                status: { type: 'keyword' },
                quantity: { type: 'double' }
              }
            }
          }
        }
      });
      logger.info('Elasticsearch index created successfully');
    } else {
      logger.info('Elasticsearch index already exists');
    }

    process.exit(0);
  } catch (error) {
    logger.error('Elasticsearch initialization failed:', error);
    process.exit(1);
  }
}

initializeElasticsearch(); 